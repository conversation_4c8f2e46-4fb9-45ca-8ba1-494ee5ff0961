// rem基础样式设置
// 设置根元素字体大小，1rem = 37.5px (基于375px设计稿)
html {
  font-size: 37.5px;
}

// 响应式适配
@media screen and (min-width: 320px) {
  html {
    font-size: 32px;
  }
}

@media screen and (min-width: 375px) {
  html {
    font-size: 37.5px;
  }
}

@media screen and (min-width: 414px) {
  html {
    font-size: 41.4px;
  }
}

@media screen and (min-width: 768px) {
  html {
    font-size: 76.8px;
  }
}

@media screen and (min-width: 1024px) {
  html {
    font-size: 102.4px;
  }
}

@media screen and (min-width: 1200px) {
  html {
    font-size: 120px;
  }
}

// 重置一些基础样式
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}
