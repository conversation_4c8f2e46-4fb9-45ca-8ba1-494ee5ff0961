@import './variables.scss';

// Element Plus 主题变量
:root {
  // 主色
  --el-color-primary: #409EFF;
  --el-color-primary-light-1: #53a8ff;
  --el-color-primary-light-2: #66b1ff;
  --el-color-primary-light-3: #79bbff;
  --el-color-primary-light-4: #8cc5ff;
  --el-color-primary-light-5: #a0cfff;
  --el-color-primary-light-6: #b3d8ff;
  --el-color-primary-light-7: #c6e2ff;
  --el-color-primary-light-8: #d9ecff;
  --el-color-primary-light-9: #ecf5ff;
  --el-color-primary-dark-1: #337ecc;
  --el-color-primary-dark-2: #2b6aab;
  --el-color-primary-rgb: 64, 158, 255;

  // 功能色
  --el-color-success: #67C23A;
  --el-color-warning: #E6A23C;
  --el-color-danger: #F56C6C;
  --el-color-info: #909399;

  // 文本色
  --el-text-color-primary: #303133;
  --el-text-color-regular: #606266;
  --el-text-color-secondary: #909399;

  // 边框和背景
  --el-border-color: #DCDFE6;
  --el-bg-color: #F5F7FA;
}

/* 重置浏览器默认样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: $font-family;
  font-size: $font-size-base;
  color: $text-color-primary;
  background-color: $background-color;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
}

ul, ol {
  list-style: none;
}

a {
  text-decoration: none;
  color: $color-primary;
  cursor: pointer;
  transition: color $transition-duration $transition-timing-function;

  &:hover {
    color: $color-primary-light;
  }
}

img {
  display: block;
  max-width: 100%;
}

button {
  border: none;
  outline: none;
  background: none;
  cursor: pointer;
}

/* Flex 布局工具类 */
.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-stretch {
  align-items: stretch;
}

/* 自定义 Element UI 样式 */
.el-button--primary {
  background-color: $color-primary;
  border-color: $color-primary;

  &:hover, &:focus {
    background-color: $color-primary-light;
    border-color: $color-primary-light;
  }
}

.el-input__inner {
  border-radius: $border-radius-base;
}

.el-dialog {
  border-radius: $border-radius-lg;
  overflow: hidden;

  .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid $border-color-light;
    margin-right: 0;
  }

  .el-dialog__body {
    padding: 24px;
  }

  .el-dialog__footer {
    padding: 16px 20px;
    border-top: 1px solid $border-color-light;
  }
}

.el-card {
  border-radius: $border-radius-lg;
  border: none;

  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid $border-color-light;
  }

  .el-card__body {
    padding: 20px;
  }
}

.el-menu {
  border-right: none;
}

.el-menu-item.is-active {
  color: $color-primary;
}

/* 公共样式类 */
.page-container {
  padding: $content-padding;
  min-height: calc(100vh - #{$header-height});
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
}

.card-shadow {
  box-shadow: $box-shadow-light;
  transition: box-shadow $transition-duration $transition-timing-function;

  &:hover {
    box-shadow: $box-shadow-base;
  }
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: $background-color;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;

  &:hover {
    background: rgba(0, 0, 0, 0.3);
  }
}

/* 动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity $transition-duration $transition-timing-function;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}
