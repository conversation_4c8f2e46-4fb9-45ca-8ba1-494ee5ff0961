import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import './styles/index.scss'

// 引入iconfont
import '@/assets/iconfont'

// 引入routerMixin
import routerMixin from '@/mixins/routerMixin'

// 引入错误处理插件
import { ErrorHandlerPlugin } from '@/utils/errorHandler'

// 添加全局路由方法
import { routerPush, routerReplace } from '@/utils/router'

// Element UI 全局配置
Vue.use(ElementUI, { size: 'small' })

// 使用错误处理插件
Vue.use(ErrorHandlerPlugin)

Vue.config.productionTip = false

// 全局注册mixin
Vue.mixin(routerMixin)

// 添加全局路由方法
Vue.prototype.goWithRefresh = function(location, forceRefresh = true) {
  return routerPush(this.$router, location, forceRefresh)
}

Vue.prototype.replaceWithRefresh = function(location, forceRefresh = true) {
  return routerReplace(this.$router, location, forceRefresh)
}

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
