<template>
  <div class="page flex-col">
    <!-- 主布局容器 -->
    <div class="box_4 flex-row">
      <!-- 左侧菜单栏 -->
      <div class="box_5 flex-col">
        <!-- 用户信息卡片 -->
        <div class="block_3 flex-row">
          <img
            class="label_2"
            referrerpolicy="no-referrer"
            :src="userAvatar"
          />
          <div class="box_7 flex-col justify-between">
            <div class="section_12 flex-row justify-between">
              <img
                class="image_2"
                referrerpolicy="no-referrer"
                src="@/assets/layouts/logo-brand.png"
              />
              <div class="text-wrapper_1 flex-col">
                <span class="text_2">{{ currentSubscriptionName || '标准版' }}</span>
              </div>
            </div>
            <span class="text_3">{{ userName }}</span>
          </div>
          <img
            class="thumbnail_5"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/arrow-right.png"
          />
        </div>

        <!-- 分割线 -->
        <img
          class="image_3"
          referrerpolicy="no-referrer"
          src="@/assets/layouts/divider-horizontal.png"
        />

        <!-- 大模型中心菜单项 -->
        <div :class="['block_4', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '大模型中心' }]" @click="handleMenuClick(menuItems[3], 3); selectMenuItem('大模型中心')">
          <img
            class="thumbnail_6"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-model-center.png"
          />
          <span class="text_4">大模型中心</span>
        </div>

        <!-- 模版中心菜单项 -->
        <div :class="['block_5', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '模版中心' }]" @click="handleSubMenuClick(menuItems[3].children[1], $event); selectMenuItem('模版中心')">
          <img
            class="thumbnail_7"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-template-center.png"
          />
          <span class="text_5">模版中心</span>
        </div>

        <!-- 应用广场（选中状态） -->
        <div :class="['section_7', 'flex-row', { 'selected': selectedMenuItem === '应用广场' }]" @click="handleSubMenuClick(menuItems[3].children[2], $event); selectMenuItem('应用广场')">
          <img
            class="thumbnail_8"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-app-square.png"
          />
          <span class="text_6">应用广场</span>
        </div>

        <!-- 分割线 -->
        <img
          class="image_4"
          referrerpolicy="no-referrer"
          src="@/assets/layouts/divider-horizontal.png"
        />

        <!-- 应用开发标题 -->
        <span class="text_7">应用开发</span>

        <!-- 智能体菜单项 -->
        <div :class="['block_6', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '智能体' }]" @click="handleSubMenuClick(menuItems[0].children[0], $event); selectMenuItem('智能体')">
          <img
            class="thumbnail_9"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-agent.png"
          />
          <span class="text_8">智能体</span>
        </div>

        <!-- 知识库菜单项 -->
        <div :class="['image-text_12', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '知识库' }]" @click="handleSubMenuClick(menuItems[0].children[1], $event); selectMenuItem('知识库')">
          <img
            class="thumbnail_10"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-knowledge.png"
          />
          <span class="text-group_1">知识库</span>
        </div>

        <!-- 数据库菜单项 -->
        <div :class="['image-text_13', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '数据库' }]" @click="handleSubMenuClick(menuItems[0].children[2], $event); selectMenuItem('数据库')">
          <img
            class="thumbnail_11"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-database.png"
          />
          <span class="text-group_2">数据库</span>
        </div>

        <!-- 插件菜单项 -->
        <div :class="['image-text_14', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '插件' }]" @click="handleSubMenuClick(menuItems[0].children[3], $event); selectMenuItem('插件')">
          <img
            class="thumbnail_12"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-plugin.png"
          />
          <span class="text-group_3">插件</span>
        </div>

        <!-- 工作流菜单项 -->
        <div :class="['image-text_15', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '工作流' }]" @click="handleSubMenuClick(menuItems[0].children[4], $event); selectMenuItem('工作流')">
          <img
            class="thumbnail_13"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-workflow.png"
          />
          <span class="text-group_4">工作流</span>
        </div>

        <!-- 分割线 -->
        <img
          class="image_5"
          referrerpolicy="no-referrer"
          src="@/assets/layouts/divider-horizontal-2.png"
        />

        <!-- 渠道接入标题 -->
        <span class="text_9">渠道接入</span>

        <!-- 新建渠道菜单项 -->
        <div :class="['image-text_16', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '新建渠道' }]" @click="handleSubMenuClick(menuItems[1].children[0], $event); selectMenuItem('新建渠道')">
          <img
            class="thumbnail_14"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-new-channel.png"
          />
          <span class="text-group_5">新建渠道</span>
        </div>

        <!-- 渠道管理菜单项 -->
        <div :class="['image-text_17', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '渠道管理' }]" @click="handleSubMenuClick(menuItems[1].children[1], $event); selectMenuItem('渠道管理')">
          <img
            class="thumbnail_15"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-channel-manage.png"
          />
          <span class="text-group_6">渠道管理</span>
        </div>

        <!-- 分割线 -->
        <img
          class="image_6"
          referrerpolicy="no-referrer"
          src="@/assets/layouts/divider-horizontal.png"
        />

        <!-- 对话管理标题 -->
        <span class="text_10">对话管理</span>

        <!-- 渠道对话监控菜单项 -->
        <div :class="['image-text_18', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '渠道对话监控' }]" @click="handleSubMenuClick(menuItems[2].children[0], $event); selectMenuItem('渠道对话监控')">
          <img
            class="thumbnail_16"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-chat-monitor.png"
          />
          <span class="text-group_7">渠道对话监控</span>
        </div>

        <!-- 我的对话菜单项 -->
        <div :class="['image-text_19', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '我的对话' }]" @click="handleSubMenuClick(menuItems[2].children[1], $event); selectMenuItem('我的对话')">
          <img
            class="thumbnail_17"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-my-chat.png"
          />
          <span class="text-group_8">我的对话</span>
        </div>

        <!-- 分割线 -->
        <img
          class="image_7"
          referrerpolicy="no-referrer"
          src="@/assets/layouts/divider-horizontal-2.png"
        />

        <!-- 个人中心标题 -->
        <span class="text_11">个人中心</span>

        <!-- 我的账户菜单项 -->
        <div :class="['image-text_20', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '我的账户' }]" @click="handleSubMenuClick(menuItems[4].children[0], $event); selectMenuItem('我的账户')">
          <img
            class="thumbnail_18"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-my-account.png"
          />
          <span class="text-group_9">我的账户</span>
        </div>

        <!-- 数据看板菜单项 -->
        <div :class="['image-text_21', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '数据看板' }]" @click="handleSubMenuClick(menuItems[4].children[1], $event); selectMenuItem('数据看板')">
          <img
            class="thumbnail_19"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-dashboard.png"
          />
          <span class="text-group_10">数据看板</span>
        </div>

        <!-- 团队空间菜单项 -->
        <div :class="['image-text_22', 'flex-row', 'justify-between', { 'selected': selectedMenuItem === '团队空间' }]" @click="handleSubMenuClick(menuItems[4].children[2], $event); selectMenuItem('团队空间')">
          <img
            class="thumbnail_20"
            referrerpolicy="no-referrer"
            src="@/assets/layouts/icon-team-space.png"
          />
          <span class="text-group_11">团队空间</span>
        </div>
      </div>

      <!-- 右侧主内容区域 -->
      <div class="box_3 flex-col">
        <!-- 顶部栏 -->
        <div class="top-header">
          <!-- Logo和应用名 -->
          <div class="logo-section">
            <img src="@/assets/logo.png" alt="logo" class="logo" />
            <span class="app-name">元智启AI</span>
            <el-popover
              placement="bottom"
              width="320"
              trigger="hover"
              popper-class="edition-popover"
            >
              <div class="edition-info">
                <div class="edition-header">
                  <div class="current-version">
                    <div class="title">当前版本</div>
                    <div class="version">{{ currentSubscriptionName || '基础版' }}</div>
                  </div>
                  <div class="expiry-date">
                    <div class="title">有效期</div>
                    <div class="date">{{ subscriptionEndDate || '未订阅' }}</div>
                  </div>
                </div>

                <div class="usage-statistics">
                  <div class="usage-item">
                    <div class="item-name">应用数量</div>
                    <div class="item-value">
                      <div class="value-text">9/30个</div>
                      <div class="progress-bar">
                        <div class="progress" style="width: 30%"></div>
                      </div>
                    </div>
                  </div>

                  <div class="usage-item">
                    <div class="item-name">工作流数量</div>
                    <div class="item-value">
                      <div class="value-text">7/15个</div>
                      <div class="progress-bar">
                        <div class="progress" style="width: 47%"></div>
                      </div>
                    </div>
                  </div>

                  <div class="usage-item">
                    <div class="item-name">知识库文件容量</div>
                    <div class="item-value">
                      <div class="value-text">37.54/3072MB</div>
                      <div class="progress-bar">
                        <div class="progress" style="width: 1.2%"></div>
                      </div>
                    </div>
                  </div>

                  <div class="usage-item">
                    <div class="item-name">知识库条数</div>
                    <div class="item-value">
                      <div class="value-text">885/100000条</div>
                      <div class="progress-bar">
                        <div class="progress" style="width: 0.9%"></div>
                      </div>
                    </div>
                  </div>

                  <div class="usage-item">
                    <div class="item-name">内置数据库总行数</div>
                    <div class="item-value">
                      <div class="value-text">87/200000行</div>
                      <div class="progress-bar">
                        <div class="progress" style="width: 0.04%"></div>
                      </div>
                    </div>
                  </div>

                  <div class="usage-item">
                    <div class="item-name">自定义插件数量</div>
                    <div class="item-value">
                      <div class="value-text">5/30个</div>
                      <div class="progress-bar">
                        <div class="progress" style="width: 17%"></div>
                      </div>
                    </div>
                  </div>

                  <div class="usage-item">
                    <div class="item-name">团队成员数量</div>
                    <div class="item-value">
                      <div class="value-text">3/10人</div>
                      <div class="progress-bar">
                        <div class="progress" style="width: 30%"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="upgrade-button" @click="handleDirectUpgrade">
                  升级版本
                </div>
              </div>
              <span class="edition" slot="reference">{{ currentSubscriptionName || '基础版' }}</span>
            </el-popover>
          </div>

          <!-- 右侧用户区域 -->
          <div class="user-section">
            <!-- 用户头像和下拉菜单 -->
            <div class="user-dropdown" @click="showUserMenu">
              <img :src="userAvatar" alt="用户头像" class="user-avatar" />
              <span class="username">{{ userName }}</span>

              <!-- 下拉菜单 -->
              <div v-show="isUserMenuVisible" class="dropdown-menu">
                <div class="user-info">
                  <img
                    class="avatar"
                    referrerpolicy="no-referrer"
                    src="@/assets/layouts/top-user-avatar.png"
                  />
                  <span class="name">{{ userName }}</span>
                </div>
                <div class="menu-divider"></div>
                <div class="menu-item" @click="handleCommand('changePassword')">
                  <img
                    class="menu-icon"
                    referrerpolicy="no-referrer"
                    src="@/assets/layouts/icon-settings.png"
                  />
                  <span>账号设置</span>
                  <img
                    class="dropdown-arrow"
                    referrerpolicy="no-referrer"
                    src="@/assets/layouts/icon-dropdown.png"
                  />
                </div>
                <div class="menu-divider"></div>
                <div class="menu-item" @click="handleCommand('logout')">
                  <img
                    class="menu-icon"
                    referrerpolicy="no-referrer"
                    src="@/assets/layouts/icon-logout.png"
                  />
                  <span>退出登录</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content-area">
          <router-view v-if="!isRouteRefreshing" :key="$route.fullPath"></router-view>
        </div>
      </div>
    </div>

    <!-- 升级专业版对话框 -->
    <el-dialog
      title="升级到专业版"
      :visible.sync="upgradeDialogVisible"
      :close-on-click-modal="false"
      width="30%">
      <span>升级到专业版可以获得更多功能和更大的配额</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="upgradeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleUpgrade">立即升级</el-button>
      </span>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码"
      :close-on-click-modal="false"
      :visible.sync="changePasswordDialogVisible"
      width="30%">
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordForm" label-width="100px">
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input type="password" v-model="passwordForm.currentPassword" placeholder="请输入当前密码"></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input type="password" v-model="passwordForm.newPassword" placeholder="请输入新密码"></el-input>
          <div class="password-tip">密码长度6-100位，只能包含字母、数字和特殊字符，必须包含至少4个不同的字符，不能包含连续3位的数字序列</div>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input type="password" v-model="passwordForm.confirmPassword" placeholder="请再次输入新密码"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="changePasswordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitChangePassword">确认修改</el-button>
      </span>
    </el-dialog>

    <!-- 团队邀请确认对话框 -->
    <el-dialog
      title="团队邀请"
      :visible.sync="inviteConfirmDialogVisible"
      :close-on-click-modal="false"
      width="400px">
      <div class="invite-confirm-content">
        <div class="invite-icon">
          <i class="el-icon-user" style="font-size: 48px; color: #409EFF;"></i>
        </div>
        <div class="invite-message">
          <p>您收到了团队邀请</p>
          <p class="invite-desc" v-if="inviteTeamName">是否要加入团队：<strong>{{ inviteTeamName }}</strong>？</p>
          <p class="invite-desc" v-else>是否要加入该团队？</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleInviteReject">取消</el-button>
        <el-button type="primary" @click="handleInviteAccept" :loading="inviteProcessing">确认加入</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { api } from '@/api/request'
import routerMixin from '@/mixins/routerMixin'
import { handleLogout } from '@/utils/auth'

export default {
  name: 'MainLayout',
  mixins: [routerMixin],
  components: {
  },
  inject: {
    reload: { default: () => () => {} }
  },
  data() {
    // 确认密码验证
    const validateConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }

    // 新密码验证
    const validateNewPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入新密码'))
      } else if (value.length < 6 || value.length > 100) {
        callback(new Error('密码长度必须在6-100个字符之间'))
      } else if (!/^[a-zA-Z0-9\W_]+$/.test(value)) {
        callback(new Error('密码只能包含字母、数字和特殊字符'))
      } else if (new Set(value.split('')).size < 4) {
        callback(new Error('密码必须包含至少4个不同的字符'))
      } else if (/012|123|234|345|456|567|678|789|987|876|765|654|543|432|321|210/.test(value)) {
        callback(new Error('密码不能包含连续3位的数字序列'))
      } else {
        callback()
      }
    }

    return {
      isRouteRefreshing: false,
      userAvatar: require('@/assets/userAvatar.png'),
      userName: '',
      activeMenuIndex: 0,
      selectedMenuItem: '应用广场', // 默认选中应用广场
      currentSubscriptionId: null,
      currentSubscriptionName: '',
      subscriptionEndDate: '',
      menuItems: [
        {
          icon: 'iconfont icon-chuangzao1',
          title: '应用开发',
          path: '/create',
          children: [
            { title: '智能体', path: '/create/app', icon: 'iconfont icon-yingyong' },
            { title: '知识库', path: '/create/knowledge', icon: 'iconfont icon-zhishiku' },
            { title: '数据库', path: '/create/database', icon: 'iconfont icon-shujuku' },
            { title: '插件', path: '/create/plugin', icon: 'iconfont icon-chajian' },
            { title: '工作流', path: '/create/workflow', icon: 'iconfont icon-gongzuoliu' }
          ]
        },
        {
          icon: 'iconfont icon-jieru',
          title: '渠道接入',
          path: '/access',
          children: [
            { title: '新建渠道', path: '/access/channel', icon: 'iconfont icon-qudaojieru' },
            { title: '渠道管理', path: '/access/client', icon: 'iconfont icon-kehuduan' }
          ]
        },
        {
          icon: 'iconfont icon-duihuaguanli',
          title: '对话管理',
          path: '/chat',
          children: [
            { title: '渠道对话监控', path: '/access/chat', icon: 'iconfont icon-duihuaguanli' },
            { title: '我的对话', path: '/access/temporary-chat', icon: 'iconfont icon-duihuaguanli' }
          ]
        },
        {
          icon: 'iconfont icon-faxian',
          title: '资源广场',
          path: '/discover',
          children: [
            { title: '模型仓库', path: '/discover/model', icon: 'iconfont icon-mobanzhongxin' },
            { title: '模板中心', path: '/discover/template-center', icon: 'iconfont icon-mobanzhongxin' },
            { title: '应用广场', path: '/discover/marketplace', icon: 'iconfont icon-yingyongguangchang' }
          ]
        },
        {
          icon: 'iconfont icon-guanli',
          title: '个人中心',
          path: '/manage',
          children: [
            { title: '我的账户', path: '/manage/account', icon: 'iconfont icon-wodezhanghu' },
            { title: '数据看板', path: '/manage/dashboard', icon: 'iconfont icon-shujukanban' },
            { title: '团队空间', path: '/manage/team', icon: 'iconfont icon-tuanduikongjian' }
          ]
        }
      ],
      isUserMenuVisible: false,
      upgradeDialogVisible: false,
      // 修改密码相关数据
      changePasswordDialogVisible: false,
      passwordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        currentPassword: [
          { required: true, message: '请输入当前密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { validator: validateNewPassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      inviteConfirmDialogVisible: false,
      inviteProcessing: false,
      inviteTeamName: ''
    }
  },
  computed: {
    activeMenu() {
      return this.$route.path
    }
  },
  watch: {
    // 监听路由变化
    // eslint-disable-next-line no-unused-vars
    '$route'(to, from) {
      // 检查是否需要强制刷新
      if (to.params.forceRefresh || to.meta.forceRefresh) {
        this.refreshRoute()
      }

      // 根据新路由更新激活的菜单索引
      this.updateActiveMenuIndex(to.path)

      // 检查新路由中是否包含邀请码
      if (to.query.inviteCode && to.query.inviteCode !== from.query.inviteCode) {
        console.log('路由变化检测到新的邀请码:', to.query.inviteCode)
        localStorage.setItem('pendingInviteCode', to.query.inviteCode)
        this.showInviteDialog(to.query.inviteCode)
      }
    }
  },
  created() {
    // 根据当前路由设置激活的菜单
    this.updateActiveMenuIndex(this.$route.path)

    // 获取用户名
    this.userName = JSON.parse(localStorage.getItem('user')).fullName || '用户'

    // 添加点击其他区域关闭用户菜单的监听
    document.addEventListener('click', this.closeUserMenu)

    // 获取当前订阅信息
    this.fetchCurrentSubscription()

    // 检查是否有待处理的邀请码
    this.checkPendingInvite()
  },
  beforeDestroy() {
    // 移除监听器
    document.removeEventListener('click', this.closeUserMenu)
  },
  methods: {
    // 选择菜单项
    selectMenuItem(itemName) {
      this.selectedMenuItem = itemName
    },

    // 添加路由刷新方法
    refreshRoute() {
      this.isRouteRefreshing = true
      this.$nextTick(() => {
        this.isRouteRefreshing = false
      })
    },

    // 根据路径更新激活的菜单索引
    updateActiveMenuIndex(currentPath) {
      // 特殊路由映射
      const routeMapping = {
        '/access/chat': 2,        // 对话管理 -> 对话菜单（索引2）
        '/access/temporary-chat': 2,     // 用户对话 -> 对话菜单（索引2）
      }

      // 首先检查特殊路由映射
      if (routeMapping[currentPath]) {
        this.activeMenuIndex = routeMapping[currentPath]
        return
      }

      // 通用路由匹配逻辑
      let matchIndex = -1

      // 按照菜单顺序查找匹配的菜单项
      for (let i = 0; i < this.menuItems.length; i++) {
        const menu = this.menuItems[i]

        // 检查是否有子菜单匹配
        if (menu.children && menu.children.length > 0) {
          const hasMatchingChild = menu.children.some(child =>
            currentPath === child.path || currentPath.startsWith(child.path + '/')
          )
          if (hasMatchingChild) {
            matchIndex = i
            break
          }
        }

        // 检查主菜单路径匹配
        if (currentPath.startsWith(menu.path)) {
          matchIndex = i
          // 不要break，让更精确的匹配覆盖
        }
      }

      // 如果找到匹配的菜单，更新activeMenuIndex
      if (matchIndex !== -1) {
        this.activeMenuIndex = matchIndex
      }
    },
    handleCommand(command) {
      if (command === 'logout') {
        handleLogout()
        this.$router.push('/login')
      } else if (command === 'changePassword') {
        this.changePasswordDialogVisible = true
      }
      this.isUserMenuVisible = false
    },
    handleMenuClick(menu, index) {
      this.activeMenuIndex = index;
      if (menu.children && menu.children.length > 0) {
        this.$router.push(menu.children[0].path).catch(err => {
          if (err.name !== 'NavigationDuplicated') {
            throw err;
          }
        });
      } else {
        this.$router.push(menu.path).catch(err => {
          if (err.name !== 'NavigationDuplicated') {
            throw err;
          }
        });
      }
    },
    handleSubMenuClick(submenu, event) {
      event.stopPropagation(); // 阻止事件冒泡
      this.$router.push(submenu.path).catch(err => {
        if (err.name !== 'NavigationDuplicated') {
          throw err;
        }
      });
    },
    showUserMenu(event) {
      event.stopPropagation(); // 阻止冒泡，避免立即触发document的click事件
      this.isUserMenuVisible = !this.isUserMenuVisible;
    },
    closeUserMenu(event) {
      // 检查点击事件是否发生在用户菜单区域外
      const userProfile = document.querySelector('.user-profile');
      const userMenu = document.querySelector('.user-menu-popup');

      if (userProfile && userMenu &&
          !userProfile.contains(event.target) &&
          !userMenu.contains(event.target)) {
        this.isUserMenuVisible = false;
      }
    },
    handleDirectUpgrade() {
      // 跳转到升级页面
      this.$router.push('/manage/team/upgrade').catch(err => {
        if (err.name !== 'NavigationDuplicated') {
          throw err;
        }
      });
    },
    handleUpgrade() {
      this.$message.success('已提交升级请求，我们将尽快处理');
      this.upgradeDialogVisible = false;
    },
    // 获取当前订阅信息
    async fetchCurrentSubscription() {
      try {
        const res = await api.subscription.getValid()
        console.log('布局中获取当前有效订阅信息:', res)
        if (res.code === 200 && res.data) {
          // 保存订阅ID
          this.currentSubscriptionId = res.data.subscriptionId
          let newUserInfo={
            ...JSON.parse(localStorage.getItem('user')),
            id:res.data.userId
          }
          // 存储更新后的用户信息
          localStorage.setItem('user',JSON.stringify(newUserInfo))
          // 格式化结束日期
          if (res.data.endDate) {
            const endDate = new Date(res.data.endDate)
            this.subscriptionEndDate = endDate.toISOString().split('T')[0]
          }
          // 获取订阅计划列表，用于匹配版本名称
          this.fetchSubscriptionPlans()
        }
      } catch (error) {
        console.error('获取当前订阅信息失败:', error)
      }
    },
    // 获取订阅计划列表
    async fetchSubscriptionPlans() {
      try {
        const res = await api.subscription.getList()
        if (res.code === 200 && res.data && res.data.items) {
          // 查找匹配的订阅计划
          this.matchSubscriptionPlan(res.data.items)
        }
      } catch (error) {
        console.error('获取订阅计划列表失败:', error)
      }
    },
    // 匹配订阅计划
    matchSubscriptionPlan(plans) {
      if (!this.currentSubscriptionId || !plans.length) return

      // 尝试匹配计划
      let matchedPlan = null

      // 尝试通过计划ID直接匹配
      matchedPlan = plans.find(plan => plan.id === this.currentSubscriptionId)

      // 如果未找到，尝试通过计划中的featureLimits[0].subscriptionId匹配
      if (!matchedPlan) {
        matchedPlan = plans.find(plan =>
          plan.featureLimits &&
          plan.featureLimits.length > 0 &&
          plan.featureLimits[0].subscriptionId === this.currentSubscriptionId
        )
      }

      // 设置匹配到的版本名称
      if (matchedPlan) {
        this.currentSubscriptionName = matchedPlan.planName
        console.log('匹配到订阅计划:', this.currentSubscriptionName)

        // 检查是否包含TeamSpace功能
        const hasTeamSpace = matchedPlan.featureLimits &&
          matchedPlan.featureLimits.some(feature =>
            feature.featureKey === 'TeamSpace' && !!feature.limitValue && feature.limitValue !== "false"
          )

        console.log('是否包含TeamSpace功能:', hasTeamSpace)

        // 将计划名称和TeamSpace功能更新到localStorage中
        const userStr = localStorage.getItem('user')
        if (userStr) {
          try {
            const userData = JSON.parse(userStr)

            // 更新用户数据
            const updatedUserData = {
              ...userData,
              subscription: {
                plan: matchedPlan.planName
              },
              teamSpace: hasTeamSpace
            }

            localStorage.setItem('user', JSON.stringify(updatedUserData))
            console.log('已更新订阅计划名称和TeamSpace功能到用户数据中')
          } catch (parseError) {
            console.error('更新订阅计划到用户数据失败:', parseError)
          }
        }
      } else {
        // 默认为基础版
        this.currentSubscriptionName = '基础版'
        console.log('未匹配到订阅计划，使用默认值:', this.currentSubscriptionName)

        // 将默认计划名称更新到localStorage中
        const userStr = localStorage.getItem('user')
        if (userStr) {
          try {
            const userData = JSON.parse(userStr)

            // 更新用户数据，默认不包含TeamSpace功能
            const updatedUserData = {
              ...userData,
              subscription: {
                plan: '基础版'
              },
              TeamSpace: false
            }

            localStorage.setItem('user', JSON.stringify(updatedUserData))
            console.log('已更新默认订阅计划到用户数据中')
          } catch (parseError) {
            console.error('更新默认订阅计划到用户数据失败:', parseError)
          }
        }
      }
    },
    // 提交修改密码
    submitChangePassword() {
      this.$refs.passwordForm.validate(async (valid) => {
        if (valid) {
          try {
            const res = await api.userAdmin.changePassword(this.passwordForm)

            if (res.isSuccess) {
              this.$message.success('密码修改成功，请重新登录')
              this.changePasswordDialogVisible = false
              // 重置表单
              this.$refs.passwordForm.resetFields()
              // 登出处理
              setTimeout(() => {
                handleLogout()
                this.$router.push('/login')
              }, 1500)
            }
          } catch (error) {
            console.error('修改密码失败:', error)
            // 检查是否已在拦截器中处理过错误
            if (!error.alreadyHandled) {
              // 显示错误消息，优先使用接口返回的错误信息
              if (error.response && error.response.data) {
                this.$showFriendlyError(error, '密码修改失败，请稍后重试')
              } else {
                this.$showFriendlyError(null, '密码修改失败，请检查网络连接后重试')
              }
            }
          }
        }
      })
    },
    // 检查是否有待处理的邀请码
    checkPendingInvite() {
      // 检查是否需要处理邀请码
      const needProcessInvite = localStorage.getItem('needProcessInvite')
      const pendingInviteCode = localStorage.getItem('pendingInviteCode')

      if (needProcessInvite === 'true' && pendingInviteCode) {
        console.log('发现待处理的邀请码，显示确认弹框')
        this.showInviteDialog(pendingInviteCode)
        // 清除标记，避免重复显示
        localStorage.removeItem('needProcessInvite')
      } else {
        // 检查当前URL是否有邀请码（用于已登录用户直接访问邀请链接的情况）
        const currentInviteCode = this.$route.query.inviteCode
        if (currentInviteCode) {
          console.log('已登录用户访问邀请链接，显示确认弹框')
          localStorage.setItem('pendingInviteCode', currentInviteCode)
          this.showInviteDialog(currentInviteCode)
        }
      }
    },
    // 处理接受邀请
    async handleInviteAccept() {
      const pendingInviteCode = localStorage.getItem('pendingInviteCode')
      if (!pendingInviteCode) {
        this.$showFriendlyError(null, '邀请码无效')
        this.inviteConfirmDialogVisible = false
        return
      }

      // 解析邀请码，只传递 | 前面的邀请码部分给接口
      const actualInviteCode = this.parseInviteCode(pendingInviteCode)

      this.inviteProcessing = true
      try {
        const response = await api.team.joinTeam(actualInviteCode)
        if (response.isSuccess) {
          this.$message.success('成功加入团队！请重新登录！')
          // 清除邀请码
          localStorage.removeItem('pendingInviteCode')

          // 登出处理，后续需要再登录时让用户选择登入的团队
          handleLogout()
          this.$router.push('/login')
        }
      } catch (error) {
        console.error('加入团队失败:', error)
      } finally {
        this.inviteProcessing = false
        this.inviteConfirmDialogVisible = false
      }
    },
    // 处理拒绝邀请
    handleInviteReject() {
      // 清除邀请码
      localStorage.removeItem('pendingInviteCode')
      localStorage.removeItem('needProcessInvite')
      this.inviteConfirmDialogVisible = false
      this.$message.info('已取消加入团队')
    },
    // 解析邀请码
    parseInviteCode(inviteCode) {
      const parts = inviteCode.split('|')
      return parts[0]
    },
    showInviteDialog(inviteCode) {
      // 解析邀请码，提取团队名称
      const parts = inviteCode.split('|')
      if (parts.length > 1) {
        this.inviteTeamName = parts[1]
      } else {
        this.inviteTeamName = ''
      }

      // 显示邀请确认弹框
      this.inviteConfirmDialogVisible = true
      console.log('显示邀请对话框，邀请码:', inviteCode, '团队名称:', this.inviteTeamName)
    }
  }
}
</script>

<style lang="scss" scoped>
// 添加 flex 布局工具类
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}

.page {
  background-color: rgba(242, 246, 252, 1);
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  .box_4 {
    width: 100%;
    height: 100%;
    display: flex;

    .box_5 {
      position: relative;
      width: 200px;
      height: 100vh;
      background: url('@/assets/layouts/sidebar-bg.png') 100% no-repeat;
      background-size: 100% 100%;
      overflow-y: auto;
      flex-shrink: 0;

      .block_3 {
        width: 168px;
        height: 80px;
        margin: 16px 16px 0 16px;
        display: flex;
        align-items: center;

        .label_2 {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          flex-shrink: 0;
        }

        .box_7 {
          width: 104px;
          height: 48px;
          margin: 0 0 0 8px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .section_12 {
            width: 104px;
            height: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .image_2 {
              width: 56px;
              height: 18px;
            }

            .text-wrapper_1 {
              height: 20px;
              background: url('@/assets/layouts/version-badge-bg.png') 100% no-repeat;
              background-size: 100% 100%;
              width: 38px;
              display: flex;
              align-items: center;
              justify-content: center;

              .text_2 {
                color: rgba(33, 135, 250, 1);
                font-size: 12px;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                text-align: center;
                white-space: nowrap;
                line-height: 12px;
              }
            }
          }

          .text_3 {
            color: rgba(17, 26, 52, 1);
            font-size: 14px;
            text-align: left;
            white-space: nowrap;
            line-height: 16px;
            margin-top: 6px;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .thumbnail_5 {
          width: 8px;
          height: 12px;
          margin-left: 8px;
        }
      }

      .image_3, .image_4, .image_5, .image_6, .image_7 {
        width: 168px;
        height: 1px;
        margin: 16px 16px 0 16px;
      }

      .text_7, .text_9, .text_10, .text_11 {
        color: rgba(186, 186, 186, 1);
        font-size: 12px;
        margin: 12px 16px 8px 16px;
        font-weight: 500;
        line-height: 16px;
      }

      .block_4 {
        margin: 8px 16px 8px 32px;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 6px 0;
        transition: background-color 0.2s;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          margin: 8px 16px;
          padding: 6px 16px;
        }

        &.selected {
          width: 168px;
          height: 40px;
          background: url('@/assets/layouts/selected-item-bg.png') 100% no-repeat;
          background-size: 100% 100%;
          margin: 8px 16px 8px 32px;
          padding: 6px 16px;
          border-radius: 8px;
        }

        .thumbnail_6 {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }

        .text_4 {
          color: rgba(0, 0, 0, 1);
          font-size: 14px;
          text-align: left;
          white-space: nowrap;
          line-height: 16px;
        }
      }

      .block_5 {
        margin: 8px 16px 8px 32px;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 6px 0;
        transition: background-color 0.2s;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          margin: 8px 16px;
          padding: 6px 16px;
        }

        &.selected {
          width: 168px;
          height: 40px;
          background: url('@/assets/layouts/selected-item-bg.png') 100% no-repeat;
          background-size: 100% 100%;
          margin: 8px 16px 8px 32px;
          padding: 6px 16px;
          border-radius: 8px;
        }

        .thumbnail_7 {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }

        .text_5 {
          color: rgba(0, 0, 0, 1);
          font-size: 14px;
          text-align: left;
          white-space: nowrap;
          line-height: 16px;
        }
      }

      .section_7 {
        width: 168px;
        height: 40px;
        background: url('@/assets/layouts/selected-item-bg.png') 100% no-repeat;
        background-size: 100% 100%;
        margin: 8px 16px 8px 32px;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 6px 16px;
        border-radius: 8px;

        .thumbnail_8 {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }

        .text_6 {
          color: rgba(0, 0, 0, 1);
          font-size: 14px;
          text-align: left;
          white-space: nowrap;
          line-height: 16px;
        }
      }
      .block_6,
      .image-text_12, .image-text_13, .image-text_14, .image-text_15,
      .image-text_16, .image-text_17, .image-text_18, .image-text_19,
      .image-text_20, .image-text_21, .image-text_22 {
        margin: 4px 16px 4px 32px;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 6px 0;
        transition: background-color 0.2s;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          margin: 4px 16px;
          padding: 6px 16px;
        }

        &.selected {
          width: 168px;
          height: 40px;
          background: url('@/assets/layouts/selected-item-bg.png') 100% no-repeat;
          background-size: 100% 100%;
          margin: 4px 16px 4px 32px;
          padding: 6px 16px;
          border-radius: 8px;
        }

        img {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }

        span {
          color: rgba(0, 0, 0, 1);
          font-size: 14px;
          text-align: left;
          white-space: nowrap;
          line-height: 16px;
        }
      }
    }

    .box_3 {
    flex: 1;
    height: 100vh;
    background: #f5f7fa;
    display: flex;
    flex-direction: column;

      .top-header {
        height: 60px;
        background: white;
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 24px;
        flex-shrink: 0;

        .logo-section {
          display: flex;
          align-items: center;

          .logo {
            width: 32px;
            height: 32px;
            margin-right: 12px;
          }

          .app-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-right: 12px;
          }

          .edition {
            font-size: 12px;
            background: rgba(33, 135, 250, 0.1);
            color: #2187FA;
            border-radius: 4px;
            padding: 4px 8px;
            cursor: pointer;
            border: 1px solid rgba(33, 135, 250, 0.2);
          }
        }

        .user-section {
          position: relative;

          .user-dropdown {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            transition: background-color 0.2s;

            &:hover {
              background: #f5f7fa;
            }

            .user-avatar {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              margin-right: 8px;
            }

            .username {
              font-size: 14px;
              color: #333;
              font-weight: 500;
            }

            .dropdown-menu {
              position: absolute;
              top: 100%;
              right: 0;
              width: 7.467rem;
              height: 5.12rem;
              background: white;
              border-radius: 0.213rem;
              box-shadow: 0 0.053rem 0.32rem 0 rgba(190, 190, 190, 0.3);
              border: 0.013rem solid rgba(223, 225, 232, 1);
              z-index: 1000;
              margin-top: 0.107rem;

              .user-info {
                width: 4.587rem;
                height: 1.28rem;
                margin: 0.534rem 0 0 0.427rem;
                display: flex;
                align-items: center;

                .avatar {
                  width: 1.28rem;
                  height: 1.28rem;
                  border-radius: 50%;
                  margin-right: 0.32rem;
                }

                .name {
                  width: 2.987rem;
                  height: 0.427rem;
                  color: rgba(17, 26, 52, 1);
                  font-size: 0.373rem;
                  font-family: PingFangSC-Medium;
                  font-weight: 500;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.427rem;
                }
              }

              .menu-item {
                background-color: rgba(244, 246, 248, 1);
                border-radius: 0.133rem;
                width: 6.614rem;
                height: 1.067rem;
                margin: 0.534rem 0 0 0.427rem;
                display: flex;
                align-items: center;
                cursor: pointer;
                transition: background-color 0.2s;

                &:hover {
                  background: rgba(230, 235, 240, 1);
                }

                .menu-icon {
                  width: 0.374rem;
                  height: 0.374rem;
                  margin: 0.347rem 0 0 0.32rem;
                }

                span {
                  width: 1.494rem;
                  height: 0.534rem;
                  color: rgba(0, 0, 0, 1);
                  font-size: 0.373rem;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.534rem;
                  margin: 0.267rem 0 0 0.32rem;
                }

                .dropdown-arrow {
                  width: 0.374rem;
                  height: 0.454rem;
                  margin: 0.32rem 0.8rem 0 2.934rem;
                }
              }

              .menu-item:last-child {
                width: 2.24rem;
                height: 0.534rem;
                margin: 0.48rem 0 0.694rem 0.72rem;
                background: transparent;

                .menu-icon {
                  width: 0.374rem;
                  height: 0.347rem;
                  margin-top: 0.107rem;
                }

                span {
                  width: 1.494rem;
                  height: 0.534rem;
                  color: rgba(0, 0, 0, 1);
                  font-size: 0.373rem;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.534rem;
                  margin-left: 0.32rem;
                }
              }
            }
          }
        }
      }

      .main-content-area {
        flex: 1;
        padding: 24px;
        overflow-y: auto;
      }
    }
  }
}

.password-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
}

.invite-confirm-content {
  text-align: center;
  padding: 20px 0;

  .invite-icon {
    margin-bottom: 20px;
  }

  .invite-message {
    p {
      margin: 0 0 8px;
      font-size: 16px;
      font-weight: 500;
      color: #303133;

      &.invite-desc {
        font-size: 14px;
        font-weight: normal;
        color: #606266;
      }
    }
  }
}
</style>

<style lang="scss">
.edition-popover {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .el-popover__title {
    margin: 0;
    padding: 0;
  }

  .edition-info {
    padding: 0;

    .edition-header {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #f0f0f0;
      padding: 14px;

      .current-version, .expiry-date {
        .title {
          font-size: 12px;
          color: #999;
          margin-bottom: 4px;
        }

        .version, .date {
          font-size: 16px;
          font-weight: bold;
          color: #333;
        }
      }
    }

    .usage-statistics {
      padding: 12px;

      .usage-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .item-name {
          width: 110px;
          font-size: 13px;
          color: #666;
        }

        .item-value {
          flex: 1;

          .value-text {
            font-size: 13px;
            color: #333;
            margin-bottom: 4px;
          }

          .progress-bar {
            width: 100%;
            height: 4px;
            background-color: #f5f5f5;
            border-radius: 2px;
            overflow: hidden;

            .progress {
              height: 100%;
              background-color: #2187FA;
              border-radius: 2px;
            }
          }
        }
      }
    }

    .upgrade-button {
      background-color: #2187FA;
      color: white;
      text-align: center;
      padding: 10px 0;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      border-radius: 0 0 8px 8px;
      transition: background-color 0.2s;

      &:hover {
        background-color: #1c78e0;
      }
    }
  }
}
</style>
