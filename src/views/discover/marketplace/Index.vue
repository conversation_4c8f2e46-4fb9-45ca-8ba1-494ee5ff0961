<template>
  <div class="discover-marketplace-page">
    <div class="page-header">
      <h2>应用广场</h2>
      <p class="page-desc">发现和使用优质的AI应用</p>
    </div>
    <div class="page-content">
      <div class="filter-bar">
        <el-input
          placeholder="搜索应用"
          v-model="searchKeyword"
          prefix-icon="el-icon-search"
          style="width: 200px"
        ></el-input>
        <el-select v-model="category" placeholder="分类" style="width: 120px; margin-left: 10px">
          <el-option label="全部" value=""></el-option>
          <el-option label="办公效率" value="office"></el-option>
          <el-option label="内容创作" value="content"></el-option>
          <el-option label="数据处理" value="data"></el-option>
          <el-option label="开发工具" value="development"></el-option>
        </el-select>
        <el-select v-model="sort" placeholder="排序" style="width: 120px; margin-left: 10px">
          <el-option label="最新" value="newest"></el-option>
          <el-option label="最热" value="popular"></el-option>
          <el-option label="评分" value="rating"></el-option>
        </el-select>
      </div>

      <el-row :gutter="20">
        <el-col :span="6" v-for="app in apps" :key="app.id">
          <el-card class="app-card" shadow="hover">
            <div class="app-logo">
              <i :class="app.icon"></i>
            </div>
            <h3>{{ app.name }}</h3>
            <p class="app-desc">{{ app.description }}</p>
            <div class="app-meta">
              <el-rate
                v-model="app.rating"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}"
              >
              </el-rate>
              <span class="download-count">{{ app.downloads }}次下载</span>
            </div>
            <div class="app-tags">
              <el-tag v-for="tag in app.tags" :key="tag">{{ tag }}</el-tag>
            </div>
            <div class="app-actions">
              <el-button type="primary" @click="handleInstall(app)">安装</el-button>
              <el-button type="text" @click="handleDetail(app)">详情</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[12, 24, 36, 48]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DiscoverMarketplacePage',
  data() {
    return {
      searchKeyword: '',
      category: '',
      sort: 'newest',
      apps: [
        {
          id: 1,
          name: '智能文档助手',
          description: '自动生成和编辑各类文档，提高办公效率',
          icon: 'el-icon-document',
          rating: 4.5,
          downloads: '2,345',
          tags: ['办公效率', '文档处理']
        },
        {
          id: 2,
          name: 'AI图像生成器',
          description: '使用AI技术生成和编辑各类图像',
          icon: 'el-icon-picture',
          rating: 4.8,
          downloads: '3,678',
          tags: ['内容创作', '图像处理']
        },
        {
          id: 3,
          name: '数据分析工具',
          description: '智能数据分析和可视化工具',
          icon: 'el-icon-data-line',
          rating: 4.6,
          downloads: '1,890',
          tags: ['数据处理', '可视化']
        },
        {
          id: 4,
          name: '代码助手',
          description: '智能代码生成和优化工具',
          icon: 'el-icon-cpu',
          rating: 4.7,
          downloads: '4,567',
          tags: ['开发工具', '编程']
        }
      ],
      pagination: {
        currentPage: 0,
        pageSize: 12,
        total: 100
      }
    }
  },
  methods: {
    handleSizeChange(val) {
      this.pagination.pageSize = val
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val
    },
    handleInstall(app) {
      this.$message.success(`开始安装：${app.name}`)
    },
    handleDetail(app) {
      this.$message.info(`查看详情：${app.name}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.discover-marketplace-page {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 10px;
      font-weight: 500;
    }

    .page-desc {
      color: #606266;
      font-size: 14px;
      margin: 0;
    }
  }

  .filter-bar {
    margin-bottom: 20px;
  }

  .app-card {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    height: 100%;

    .app-logo {
      text-align: center;
      margin-bottom: 15px;

      i {
        font-size: 48px;
        color: #409EFF;
      }
    }

    h3 {
      margin: 0 0 10px;
      font-size: 16px;
      color: #303133;
    }

    .app-desc {
      margin: 0 0 15px;
      color: #606266;
      font-size: 14px;
      line-height: 1.4;
      height: 40px;
      overflow: hidden;
    }

    .app-meta {
      margin-bottom: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .download-count {
        color: #909399;
        font-size: 12px;
      }
    }

    .app-tags {
      margin-bottom: 15px;

      .el-tag {
        margin-right: 5px;
        margin-bottom: 5px;
      }
    }

    .app-actions {
      margin-top: auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .pagination {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
