<template>
  <div class="discover-page">
    <router-view v-if="$route.path !== '/discover'"></router-view>
    <div v-else>
      <h2>发现</h2>
      <div class="discover-content">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="模板中心" name="templates">
            <el-row :gutter="20">
              <el-col :span="8" v-for="template in templates" :key="template.id">
                <el-card class="template-card">
                  <img :src="template.icon" class="template-icon">
                  <h3>{{ template.name }}</h3>
                  <p>{{ template.description }}</p>
                  <div class="template-actions">
                    <el-button type="text" @click="viewTemplate(template)">查看详情</el-button>
                    <el-button type="primary" @click="useTemplate(template)">使用模板</el-button>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="应用广场" name="apps">
            <el-row :gutter="20">
              <el-col :span="8" v-for="app in popularApps" :key="app.id">
                <el-card class="app-card">
                  <img :src="app.icon" class="app-icon">
                  <h3>{{ app.name }}</h3>
                  <p>{{ app.description }}</p>
                  <div class="app-actions">
                    <el-button type="primary" @click="viewApp(app)">查看详情</el-button>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DiscoverPage',
  data() {
    return {
      activeTab: 'templates',
      templates: [
        {
          id: 1,
          name: '智能客服模板',
          description: '快速构建智能客服系统',
          icon: 'https://example.com/icon1.png'
        },
        {
          id: 2,
          name: '知识库助手',
          description: '构建智能知识库问答系统',
          icon: 'https://example.com/icon2.png'
        }
      ],
      popularApps: [
        {
          id: 1,
          name: '医疗诊断助手',
          description: '智能医疗诊断系统',
          icon: 'https://example.com/icon3.png'
        },
        {
          id: 2,
          name: '法律咨询助手',
          description: '智能法律咨询系统',
          icon: 'https://example.com/icon4.png'
        }
      ]
    }
  },
  methods: {
    useTemplate(template) {
      this.$message.info('使用模板 "' + template.name + '" 功能开发中...')
    },
    viewTemplate(template) {
      this.$message.info('查看模板 "' + template.name + '" 详情功能开发中...')
    },
    viewApp(app) {
      this.$message.info('查看应用 "' + app.name + '" 详情功能开发中...')
    }
  }
}
</script>

<style lang="scss" scoped>
.discover-page {
  padding: 20px;
  height: 100%;

  .discover-content {
    margin-top: 20px;

    .template-list,
    .popular-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .template-card,
    .popular-card {
      .template-icon,
      .popular-icon {
        text-align: center;
        margin-bottom: 15px;

        img {
          width: 60px;
          height: 60px;
          border-radius: 8px;
        }
      }

      .template-name,
      .popular-name {
        margin: 0 0 10px;
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }

      .template-desc,
      .popular-desc {
        margin: 0 0 15px;
        font-size: 14px;
        color: #606266;
        height: 40px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .template-footer,
      .popular-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
}
</style>
