<template>
  <div class="app-page">
    <router-view v-if="$route.path !== '/create/app'"></router-view>
    <div class="app-list" v-else>
      <div
        v-if="loading"
        class="loading-container"
        v-loading="loading"
        element-loading-background="rgba(255, 255, 255, 0.7)"
        element-loading-text="加载应用中..."
      ></div>
      <div v-else class="app-container">
        <div
          v-for="(app, index) in apps"
          :key="app.id"
          class="app-card"
          :style="{ '--card-index': index }"
          @click="handleSettings(app)"
        >
          <div class="app-card-main">
            <div class="app-header">
              <div class="app-avatar">
                <el-avatar :size="44" :src="app.profilePhoto">{{
                  app.name.charAt(0)
                }}</el-avatar>
              </div>
              <div class="app-actions">
                <div class="action-icon" @click.stop="handleShare(app)">
                  <i class="el-icon-share"></i>
                </div>
                <div class="action-icon" @click.stop="handleCopy(app)">
                  <i class="el-icon-document-copy"></i>
                </div>
                <div class="action-icon" @click.stop="handleSettings(app)">
                  <i class="el-icon-setting"></i>
                </div>
              </div>
            </div>
            <el-tooltip
              class="item"
              effect="dark"
              :content="app.name"
              placement="top-start"
            >
              <h3 class="app-name">{{ app.name }}</h3>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              :content="app.description"
              placement="top-start"
            >
              <p class="app-desc">{{ app.description }}</p>
            </el-tooltip>
            <div class="app-divider"></div>
            <div class="app-footer">
              <div class="left-section">
                <div class="app-type" :class="+app.sessionFlowCode===1 ? 'light' : ''">
                  <span>{{ getSessionFlowCodeLabel(app.sessionFlowCode) }}</span>
                </div>
                <!-- <div class="app-code">code：{{ app.sessionFlowCode }}</div> -->
              </div>
              <div
                class="app-chat-btn"
                :class="{ loading: chatLoadingMap[app.id] }"
                @click.stop="handleChat(app)"
              >
                <i v-if="chatLoadingMap[app.id]" class="el-icon-loading"></i>
                <span>开始对话</span>
              </div>
            </div>
          </div>
        </div>

        <div class="app-card create-card" @click="handleCreate">
          <div class="create-content">
            <div class="create-icon">
              <i class="el-icon-plus"></i>
            </div>
            <div class="create-text">创建智能体</div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      title="智能体复制"
      :visible.sync="copyDialogVisible"
      width="400px"
      :close-on-click-modal="false"
      center
    >
      <div class="copy-dialog-content">
        <p>将自动创建一个相同的应用，确认复制吗？</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="copyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmCopy">确定复制</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="分享应用"
      :visible.sync="shareDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      center
    >
      <div class="share-dialog-content">
        <h3>已为您生成应用的专属链接</h3>
        <div class="share-link">
          <el-input v-model="shareLink" readonly>
            <template #append>
              <i
                :class="linkCopied ? 'el-icon-check' : 'el-icon-document-copy'"
                @click="copyShareLink"
              ></i>
            </template>
          </el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="shareDialogVisible = false"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { api } from "@/api/request"
import { copyToClipboard, generateShareLink } from "@/utils"
import { getAgentTypeLabel } from "@/utils/enums"

export default {
  name: "AppPage",
  data() {
    return {
      apps: [],
      copyDialogVisible: false,
      shareDialogVisible: false,
      currentApp: null,
      shareLink: "",
      linkCopied: false,
      loading: false,
      total: 0,
      chatLoadingMap: {}, // 记录每个应用的聊天loading状态
    };
  },
  created() {
    this.fetchAppList();
  },
  methods: {
    // 获取sessionFlowCode对应的中文标签
    getSessionFlowCodeLabel(code) {
      // 将字符串转换为数字再获取标签
      const numCode = parseInt(code);
      return getAgentTypeLabel(numCode) || code;
    },
    async fetchAppList() {
      try {
        this.loading = true;
        let params = {
          sorting: 0,
          maxResultCount: 1000,
        };
        const response = await api.sessionFlow.getList(params);
        if (response.code === 200 && response.data) {
          this.total = response.data.totalCount;
          this.apps = response.data.items || [];
        } else {
          throw new Error(response.message || "获取应用列表失败");
        }
      } catch (error) {
        console.error("获取应用列表失败:", error);
        this.$showFriendlyError(error, "获取应用列表失败");
      } finally {
        this.loading = false;
      }
    },
    handleCreate() {
      this.$router.push("/create/app/new").catch((err) => {
        if (err.name !== "NavigationDuplicated") {
          throw err;
        }
      });
    },
    handleKnowledge(app) {
      this.$message.info(`进入知识智能体：${app.name}`);
    },
    async handleChat(app) {
      // 防止重复点击
      if (this.chatLoadingMap[app.id]) {
        return;
      }

      try {
        // 设置loading状态
        this.$set(this.chatLoadingMap, app.id, true);

        // 调用创建SignalR通道的接口
        console.log("开始为应用创建聊天会话:", app.id);
        const result = await this.createSignalRChannel(app.id);

        this.$message.success("聊天会话创建成功，正在打开聊天窗口...");

        // 接口成功后进行页面跳转，传递会话ID
        const sessionId = result.sessionData.sessionId;
        // 使用路由跳转到/access/temporary-chat，并在新标签页打开
        const routeUrl = this.$router.resolve({
          path: "/access/temporary-chat",
          query: {
            id: app.id,
            sourceType: 1,
            sessionId: sessionId,
          },
        });
        window.open(routeUrl.href, "_blank");
      } catch (error) {
        console.error("创建聊天通道失败:", error);
        this.$showFriendlyError(error, "创建聊天通道失败，请重试");
      } finally {
        // 清除loading状态
        this.$set(this.chatLoadingMap, app.id, false);
      }
    },

    // 创建SignalR通道的接口
    async createSignalRChannel(appId) {
      try {
        // 第一步：创建聊天会话
        const sessionResponse = await this.createChatSession(appId);

        if (!sessionResponse.isSuccess) {
          throw new Error(sessionResponse.message || "创建聊天会话失败");
        }

        const sessionData = sessionResponse.data;
        console.log(`为应用 ${appId} 创建聊天会话成功:`, sessionData);

        // 返回会话信息，用于跳转时传递
        return {
          success: true,
          sessionData: sessionData,
          message: "聊天会话创建成功",
        };
      } catch (error) {
        console.error("创建聊天会话失败:", error);
        throw error;
      }
    },

    // 创建聊天会话的接口
    async createChatSession(appId) {
      try {
        const response = await api.chat.createSession({
          clientId: appId,
          sourceType: 1, // 1: 应用
          sessionName: `应用${appId}的聊天会话`,
          isTestSession: true,
        });

        return response;
      } catch (error) {
        console.error("创建聊天会话失败:", error);
        return {
          isSuccess: false,
          message:
            error.response?.data?.message ||
            error.message ||
            "创建聊天会话失败",
        };
      }
    },
    handleCopy(app) {
      this.currentApp = app;
      this.copyDialogVisible = true;
    },
    async confirmCopy() {
      if (this.currentApp) {
        try {
          // 先获取应用详情
          const detailResponse = await api.sessionFlow.getDetail(
            this.currentApp.id
          );
          if (detailResponse.code !== 200 || !detailResponse.data) {
            throw new Error(detailResponse.message || "获取应用详情失败");
          }

          const appDetail = detailResponse.data;
          appDetail.name = `${appDetail.name} - 副本`;
          delete appDetail.id;
          appDetail["createFlowDetailInput"] = appDetail.flowDetailDto;
          delete appDetail.flowDetailDto;

          const res = await api.sessionFlow.create(appDetail);

          if (res.code === 200) {
            this.$message.success("智能体复制成功");
            this.fetchAppList();
          } else {
            throw new Error(res.message || "复制失败");
          }
        } catch (error) {
          console.error("复制应用失败:", error);
          this.$showFriendlyError(error, "复制失败，请稍后重试");
        }
      }
      this.copyDialogVisible = false;
      this.currentApp = null;
    },
    handleShare(app) {
      this.currentApp = app;
      this.shareDialogVisible = true;
      this.shareLink = generateShareLink(app.id);
      this.linkCopied = false;
    },
    handleSettings(app) {
      this.$router.push(`/create/app/settings/${app.id}`);
    },
    async copyShareLink() {
      try {
        const success = await copyToClipboard(this.shareLink);
        if (success) {
          this.linkCopied = true;
          this.$message.success("链接已复制到剪贴板");
        } else {
          this.$showFriendlyError(null, "复制失败，请手动复制");
        }
      } catch (err) {
        console.error("复制失败:", err);
        this.$showFriendlyError(err, "复制失败，请手动复制");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.app-page {
  width: 100%;
  height: 100%;
  padding: 24px;
  box-sizing: border-box;
  background-color: #f5f7fa;
  border-radius: 12px;
}

.app-list {
  width: 100%;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.app-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.app-card {
  position: relative;
  width: 100%;
  height: 195px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  border: 1px solid #eaeaea;
  box-sizing: border-box;
  animation: fadeInUp 0.5s ease forwards;
  animation-delay: calc(var(--card-index, 0) * 0.1s);
  opacity: 0;
  transform-origin: center;

  &:hover {
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: scale(0.98);
  }

  .app-card-main {
    width: 100%;
    height: 100%;
    position: relative;
    box-sizing: border-box;
  }

  .app-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0;
    padding: 16px 20px 0;
    height: 44px;

    .app-avatar {
      .el-avatar {
        width: 44px;
        height: 44px;
        font-size: 16px;
      }
    }

    .app-actions {
      display: flex;
      gap: 12px;

      .action-icon {
        width: 16px;
        height: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: all 0.2s;

        &:hover {
          opacity: 0.8;
        }

        i {
          font-size: 16px;
          color: #666;
        }
      }
    }
  }

  .app-name {
    position: absolute;
    top: 76px;
    left: 20px;
    right: 20px;
    font-size: 16px;
    font-weight: 600;
    color: rgba(0, 0, 0, 1);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 16px;
    text-align: left;
    margin: 0;
    height: 16px;
    font-family: PingFangSC-Semibold, PingFang SC, -apple-system,
      BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial,
      sans-serif;
  }

  .app-desc {
    position: absolute;
    top: 102px;
    left: 20px;
    right: 20px;
    font-size: 14px;
    color: rgba(102, 102, 102, 1);
    line-height: 20px;
    height: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    font-weight: normal;
    margin: 0;
    display: block;
  }

  .app-divider {
    position: absolute;
    top: 142px;
    left: 20px;
    right: 20px;
    height: 1px;
    background-color: #eaeaea;
    margin: 0;
  }

  .app-footer {
    position: absolute;
    left: 20px;
    right: 20px;
    bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 28px;

    .left-section {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;
      overflow: hidden;

      .app-type {
        flex-shrink: 0;
        border-radius: 4px;
        padding: 0 4px;
        color: #e6a23c;
        background-color: #fdf6ec;
        border: 1px solid #faecd8;

        &.light {
          color: #409eff;
          background-color: #ecf5ff;
          border-color: #d9ecff;
        }

        span {
          font-size: 12px;
          line-height: 12px;
          white-space: nowrap;
          font-weight: normal;
        }
      }

      .app-code {
        margin-left: 10px;
        font-size: 12px;
        color: rgba(102, 102, 102, 1);
        line-height: 28px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        min-width: 0;
      }
    }

    .app-chat-btn {
      flex-shrink: 0;
      border: 1px solid rgba(33, 135, 250, 1);
      border-radius: 4px;
      height: 28px;
      width: 80px;
      position: relative;
      cursor: pointer;
      box-sizing: border-box;
      margin-left: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      background-color: #ffffff;
      transition: all 0.3s ease;

      span {
        font-size: 12px;
        color: rgba(33, 135, 250, 1);
        font-weight: 500;
        white-space: nowrap;
        line-height: 12px;
        font-family: PingFangSC-Medium, PingFang SC, -apple-system,
          BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial,
          sans-serif;
        transition: color 0.3s ease;
      }

      i {
        font-size: 12px;
        color: rgba(33, 135, 250, 1);
        animation: rotate 1s linear infinite;
        transition: color 0.3s ease;
      }

      &:hover:not(.loading) {
        background-color: rgba(33, 135, 250, 1);

        span {
          color: #ffffff;
        }

        i {
          color: #ffffff;
        }
      }

      &.loading {
        cursor: not-allowed;
        opacity: 0.8;
        background-color: rgba(33, 135, 250, 0.02);
      }
    }
  }
}

.create-card {
  position: relative;
  width: 100%;
  height: 195px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  border: 1px solid #eaeaea;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  animation: fadeInUp 0.5s ease forwards;
  animation-delay: 0.3s;
  opacity: 0;

  &:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    background: var(--el-color-primary-light-9);

    .create-content {
      color: var(--el-color-primary);
    }

    .create-icon {
      color: var(--el-color-primary);

      i {
        transform: rotate(90deg);
      }
    }

    .create-text {
      color: var(--el-color-primary);
    }
  }

  &:active {
    transform: scale(0.98);
  }

  .create-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: #9ca3af;
    transition: color 0.3s ease;
  }

  .create-icon {
    font-size: 32px;
    transition: all 0.3s ease;

    i {
      transition: all 0.3s ease;
    }
  }

  .create-text {
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
  }
}

.share-dialog-content,
.copy-dialog-content {
  text-align: center;
  padding: 20px 0;
}

.share-link {
  margin-top: 20px;

  .el-input-group__append {
    cursor: pointer;
  }
}

.loading-container {
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .loading-text {
    color: #909399;
    margin-top: 120px;
    font-size: 14px;
  }
}
:deep(.el-avatar > img) {
  width: 100%;
  height: 100%;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
