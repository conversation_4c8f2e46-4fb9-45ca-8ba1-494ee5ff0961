# 高级配置组件
<template>
  <div class="settings-content" :class="{ 'debug-layout': isDebugging }">
    <!-- 调试模式下的左右布局 -->
    <div v-if="isDebugging" class="debug-container">
      <!-- 左侧配置区域 -->
      <div class="config-area">
        <div class="config-sections">
          <div class="settings-card">
            <div class="card-header">
              <h2 class="main-title">智能转人工</h2>
            </div>

            <div class="settings-section">
              <div class="config-row">
                <div class="config-item">
                  <div class="item-label">配置项</div>
                  <div class="item-status">状态</div>
                  <div class="item-action">操作</div>
                </div>
              </div>

              <div class="config-row">
                <div class="config-item">
                  <div class="item-label">转人工设置</div>
                  <div class="item-status">
                    <el-switch
                      v-model="humanTransferConfig.nodeTypeConfig.isEnable"
                      @change="updateHumanTransferConfig"
                    />
                  </div>
                  <div class="item-action">
                    <el-button type="text" @click="handleConfig">配置</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="settings-card">
            <div class="card-header">
              <h2 class="main-title">拟人化</h2>
            </div>

            <div class="settings-section">
              <div class="config-row">
                <div class="config-item">
                  <div class="item-label">分段回复</div>
                  <div class="item-action">
                    <el-switch
                      v-model="responseConfig.enableSegmentedResponse"
                      @change="updateResponseConfig"
                    />
                  </div>
                </div>
              </div>

              <div class="config-row">
                <div class="config-item">
                  <div class="item-label">合并回复</div>
                  <div class="item-action">
                    <el-switch
                      v-model="responseConfig.enableMergeResponse"
                      @change="updateResponseConfig"
                    />
                  </div>
                </div>
              </div>
              <div class="config-row">
                <div class="config-item">
                  <div class="item-label">提示词优化</div>
                  <div class="item-action">
                    <el-switch
                      v-model="responseConfig.tipOptimization"
                      @change="updateResponseConfig"
                    />
                  </div>
                </div>
              </div>
              <div class="config-row">
                <div class="config-item">
                  <div class="item-label">延迟回复</div>
                  <div
                    class="item-action"
                    style="
                      display: flex;
                      align-items: center;
                      gap: 10px;
                      width: 200px;
                    "
                  >
                    <el-input-number
                      v-model="responseConfig.responseDelay"
                      :min="0"
                      :max="60"
                      controls-position="right"
                      @change="updateResponseConfig"
                    ></el-input-number>
                    <span style="margin-left: 5px">秒</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

            <!-- 右侧调试区域 -->
      <div class="debug-area">
        <div class="debug-panel">
          <div class="debug-header">
            <h3>调试面板</h3>
            <el-button
              type="text"
              size="mini"
              @click="$emit('toggle-debug')"
              class="close-debug-btn"
            >
              <i class="el-icon-close"></i>
            </el-button>
          </div>
          <div class="debug-content">
            <div class="debug-chat-container">
              <DebugChatPanel
                :app-info="getDebugAppInfo()"
                :app-id="$route.params.id"
                :source-type="1"
                debug-mode="advanced"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 非调试模式下的原始布局 -->
    <div v-else>
      <div class="settings-card">
      <div class="card-header">
        <h2 class="main-title">智能转人工</h2>
      </div>

      <div class="settings-section">
        <div class="config-row">
          <div class="config-item">
            <div class="item-label">配置项</div>
            <div class="item-status">状态</div>
            <div class="item-action">操作</div>
          </div>
        </div>

        <div class="config-row">
          <div class="config-item">
            <div class="item-label">转人工设置</div>
            <div class="item-status">
              <el-switch
                v-model="humanTransferConfig.nodeTypeConfig.isEnable"
                @change="updateHumanTransferConfig"
              />
            </div>
            <div class="item-action">
              <el-button type="text" @click="handleConfig">配置</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="settings-card">
      <div class="card-header">
        <h2 class="main-title">拟人化</h2>
      </div>

      <div class="settings-section">
        <div class="config-row">
          <div class="config-item">
            <div class="item-label">分段回复</div>
            <div class="item-action">
              <el-switch
                v-model="responseConfig.enableSegmentedResponse"
                @change="updateResponseConfig"
              />
            </div>
          </div>
        </div>

        <div class="config-row">
          <div class="config-item">
            <div class="item-label">合并回复</div>
            <div class="item-action">
              <el-switch
                v-model="responseConfig.enableMergeResponse"
                @change="updateResponseConfig"
              />
            </div>
          </div>
        </div>
        <div class="config-row">
          <div class="config-item">
            <div class="item-label">提示词优化</div>
            <div class="item-action">
              <el-switch
                v-model="responseConfig.tipOptimization"
                @change="updateResponseConfig"
              />
            </div>
          </div>
        </div>
        <div class="config-row">
          <div class="config-item">
            <div class="item-label">延迟回复</div>
            <div
              class="item-action"
              style="
                display: flex;
                align-items: center;
                gap: 10px;
                width: 200px;
              "
            >
              <el-input-number
                v-model="responseConfig.responseDelay"
                :min="0"
                :max="60"
                controls-position="right"
                @change="updateResponseConfig"
              ></el-input-number>
              <span style="margin-left: 5px">秒</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 转人工配置抽屉 -->
    <el-drawer
      title="转人工配置"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="50%"
      :show-close="true"
      custom-class="config-drawer"
    >
      <div class="drawer-content">
        <!-- 触发配置 -->
        <div class="config-section">
          <div class="section-header">
            <h3>触发配置</h3>
          </div>
          <div class="section-content">
            <div
              v-for="(trigger, index) in humanTransferConfig.nodeTypeConfig
                .triggerConditions"
              :key="index"
              class="trigger-item"
            >
              <div class="trigger-content">
                <div class="trigger-row">
                  <div class="trigger-col" style="flex: 1">
                    <span class="label"
                      >触发方式
                      <el-tooltip
                        content="当用户最近一次提问满足下列设置的任意条件时，可触发转人工"
                        placement="top"
                      >
                        <i class="el-icon-question"></i>
                      </el-tooltip>
                    </span>
                    <el-select v-model="trigger.matchType" placeholder="请选择">
                      <el-option label="关键词匹配" :value="1" />
                      <el-option label="意图识别" :value="2" />
                    </el-select>
                  </div>
                  <div class="trigger-col" style="flex: 2">
                    <span class="label"
                      >意图/关键词
                      <el-tooltip
                        content="触发方式为关键词匹配，设置关键词，当用户提问包含关键词则转人工
触发方式为意图识别，用自然语言描述触发转人工的用户意图，当用户提问的语义满足该意图则转人工"
                        placement="top"
                      >
                        <i class="el-icon-question"></i>
                      </el-tooltip>
                    </span>

                    <el-input v-model="trigger.keyWord" placeholder="请输入">
                    </el-input>
                  </div>
                  <div class="trigger-col">
                    <el-button
                      type="text"
                      class="delete-btn"
                      icon="el-icon-delete"
                      @click="handleDeleteTrigger(index)"
                      >删除</el-button
                    >
                  </div>
                </div>
              </div>
            </div>
            <div class="add-trigger">
              <el-button
                type="dashed"
                icon="el-icon-plus"
                @click="handleAddTrigger"
                >新增触发配置</el-button
              >
            </div>
          </div>
        </div>

        <!-- 回复配置 -->
        <div class="config-section">
          <div class="section-header">
            <h3>回复配置</h3>
          </div>
          <div class="section-content">
            <div class="reply-row">
              <span class="label">默认回复：</span>
              <el-tooltip
                content="触发转人工后，智能体向用户发送的默认回复（仅回复一次）"
                placement="top"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <el-input
              type="textarea"
              v-model="humanTransferConfig.nodeTypeConfig.defaultReply"
              :rows="3"
              placeholder="请输入"
            />
            <div class="reply-row mt-16">
              <span class="label">回复模式：</span>
              <el-tooltip
                content="转为人工后，有三种模式可选

1. 不回复：转人工后，AI不回复用户的后续提问，需在「对话管理」中手动切换为AI回复后方能继续回复
2. 继续回复：转人工后，AI仍继续回复用户的后续提问
3. 延迟回复：转人工后，AI会在指定的延迟时间后自动切换为AI回复，在此期间不回复用户"
                placement="top"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <el-select
              v-model="humanTransferConfig.nodeTypeConfig.replyMode"
              placeholder="请选择回复模式"
              style="width: 100%"
            >
              <el-option label="转人工后不回复" :value="1" />
              <!-- <el-option label="转人工后继续回复" :value="2" />
              <el-option label="转人工后延迟回复" :value="3" /> -->
            </el-select>
            <div
              class="reply-row mt-16"
              v-if="humanTransferConfig.nodeTypeConfig.replyMode == 3"
            >
              <span class="label">延迟时间(分钟)：</span>
              <el-input-number
                v-model="humanTransferConfig.nodeTypeConfig.delayMinutes"
                :min="1"
                :max="60"
              ></el-input-number>
            </div>
          </div>
        </div>
      </div>

      <!-- 抽屉底部按钮 -->
      <div class="drawer-footer">
        <el-button @click="drawerVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-drawer>
    </div>

    <!-- 转人工配置抽屉在非调试模式下也需要 -->
    <el-drawer
      title="转人工配置"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="50%"
      :show-close="true"
      custom-class="config-drawer"
    >
      <div class="drawer-content">
        <!-- 触发配置 -->
        <div class="config-section">
          <div class="section-header">
            <h3>触发配置</h3>
          </div>
          <div class="section-content">
            <div
              v-for="(trigger, index) in humanTransferConfig.nodeTypeConfig
                .triggerConditions"
              :key="index"
              class="trigger-item"
            >
              <div class="trigger-content">
                <div class="trigger-row">
                  <div class="trigger-col" style="flex: 1">
                    <span class="label"
                      >触发方式
                      <el-tooltip
                        content="当用户最近一次提问满足下列设置的任意条件时，可触发转人工"
                        placement="top"
                      >
                        <i class="el-icon-question"></i>
                      </el-tooltip>
                    </span>
                    <el-select v-model="trigger.matchType" placeholder="请选择">
                      <el-option label="关键词匹配" :value="1" />
                      <el-option label="意图识别" :value="2" />
                    </el-select>
                  </div>
                  <div class="trigger-col" style="flex: 2">
                    <span class="label"
                      >意图/关键词
                      <el-tooltip
                        content="触发方式为关键词匹配，设置关键词，当用户提问包含关键词则转人工
触发方式为意图识别，用自然语言描述触发转人工的用户意图，当用户提问的语义满足该意图则转人工"
                        placement="top"
                      >
                        <i class="el-icon-question"></i>
                      </el-tooltip>
                    </span>

                    <el-input v-model="trigger.keyWord" placeholder="请输入">
                    </el-input>
                  </div>
                  <div class="trigger-col">
                    <el-button
                      type="text"
                      class="delete-btn"
                      icon="el-icon-delete"
                      @click="handleDeleteTrigger(index)"
                      >删除</el-button
                    >
                  </div>
                </div>
              </div>
            </div>
            <div class="add-trigger">
              <el-button
                type="dashed"
                icon="el-icon-plus"
                @click="handleAddTrigger"
                >新增触发配置</el-button
              >
            </div>
          </div>
        </div>

        <!-- 回复配置 -->
        <div class="config-section">
          <div class="section-header">
            <h3>回复配置</h3>
          </div>
          <div class="section-content">
            <div class="reply-row">
              <span class="label">默认回复：</span>
              <el-tooltip
                content="触发转人工后，智能体向用户发送的默认回复（仅回复一次）"
                placement="top"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <el-input
              type="textarea"
              v-model="humanTransferConfig.nodeTypeConfig.defaultReply"
              :rows="3"
              placeholder="请输入"
            />
            <div class="reply-row mt-16">
              <span class="label">回复模式：</span>
              <el-tooltip
                content="转为人工后，有三种模式可选

1. 不回复：转人工后，AI不回复用户的后续提问，需在「对话管理」中手动切换为AI回复后方能继续回复
2. 继续回复：转人工后，AI仍继续回复用户的后续提问
3. 延迟回复：转人工后，AI会在指定的延迟时间后自动切换为AI回复，在此期间不回复用户"
                placement="top"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <el-select
              v-model="humanTransferConfig.nodeTypeConfig.replyMode"
              placeholder="请选择回复模式"
              style="width: 100%"
            >
              <el-option label="转人工后不回复" :value="1" />
              <!-- <el-option label="转人工后继续回复" :value="2" />
              <el-option label="转人工后延迟回复" :value="3" /> -->
            </el-select>
            <div
              class="reply-row mt-16"
              v-if="humanTransferConfig.nodeTypeConfig.replyMode == 3"
            >
              <span class="label">延迟时间(分钟)：</span>
              <el-input-number
                v-model="humanTransferConfig.nodeTypeConfig.delayMinutes"
                :min="1"
                :max="60"
              ></el-input-number>
            </div>
          </div>
        </div>
      </div>

      <!-- 抽屉底部按钮 -->
      <div class="drawer-footer">
        <el-button @click="drawerVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-drawer>
  </div>
</template>


<script>
import DebugChatPanel from "@/components/chat/DebugChatPanel.vue"

export default {
  name: "AdvancedConfig",
  components: {
    DebugChatPanel
  },
  props: {
    flowDetail: {
      type: Object,
      default: () => ({})
    },
    isDebugging: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 拟人化响应配置，对应结束节点数据
      responseConfig: {
        enableSegmentedResponse: false,
        enableMergeResponse: true,
        responseDelay: 0,
        tipOptimization: true,
      },
      drawerVisible: false,

      // 转人工配置，与接口数据对应
      humanTransferConfig: {
        activeTab: "config",
        label: "转人工",
        name: "转人工",
        description: "转人工节点描述",
        nodeTypeConfig: {
          memory: false,
          memoryTurns: 1,
          defaultReply: "",
          notifyPerson: "staff1",
          delayMinutes: 5,
          replyMode: 3,
          inputs: [],
          outputs: [],
          content: "",
          isEnable: true,
          name: null,
          notifyMethods: null,
          structuredOutput: false,
          triggerConditions: [
            {
              matchType: 1,
              operatorType: 0,
              keyWord: "",
            },
          ],
        },
        type: "humanTransfer",
        id: "",
      },
    };
  },
  created() {
    // 初始化配置
    this.initHumanTransferConfig();
    this.initResponseConfig();

    // 初始化后立即向父组件传递配置数据
    this.$nextTick(() => {
      this.updateHumanTransferConfig();
      this.updateResponseConfig();
    });
  },
  watch: {
    flowDetail: {
      handler(newVal) {
        if (newVal && newVal.flowDetailDto) {
          this.initHumanTransferConfig(newVal.flowDetailDto);
          this.initResponseConfig(newVal.flowDetailDto);
        }
      },
      immediate: true,
      deep: true
    },
    humanTransferConfig: {
      handler: "updateHumanTransferConfig",
      deep: true
    }
  },
  methods: {
    // 获取调试应用信息
    getDebugAppInfo() {
      const flowDetail = this.flowDetail || {};
      return {
        id: this.$route.params.id,
        name: flowDetail.name || "高级调试应用",
        description: flowDetail.description || "正在调试应用的高级配置",
        profilePhoto: flowDetail.profilePhoto || "",
        introduce: flowDetail.introduce || "这是高级配置调试环境，您可以测试应用的高级功能如转人工、拟人化响应等。"
      };
    },
    // 初始化拟人化响应配置
    initResponseConfig(flowData) {
      if (
        flowData &&
        flowData.endNodes &&
        flowData.endNodes.length > 0
      ) {
        let endNode= flowData.endNodes[0]
          // 从结束节点获取responseConfig数据
          if (endNode.data.nodeTypeConfig.responseConfig) {
              const rc = endNode.data.nodeTypeConfig.responseConfig;

              // 更新响应配置
              this.responseConfig = {
                enableSegmentedResponse:
                  rc.enableSegmentedResponse !== undefined
                    ? rc.enableSegmentedResponse
                    : false,
                enableMergeResponse:
                  rc.enableMergeResponse !== undefined
                    ? rc.enableMergeResponse
                    : true,
                responseDelay:
                  rc.responseDelay !== undefined ? rc.responseDelay : 0,
                tipOptimization:
                  rc.tipOptimization !== undefined ? rc.tipOptimization : true,
              }
            }
      }
    },
    // 初始化转人工配置
    initHumanTransferConfig(flowData) {
      if (
        flowData &&
        flowData.humanTransferNodes &&
        flowData.humanTransferNodes.length > 0
      ) {
        const humanTransferNode = flowData.humanTransferNodes[0];
        if (humanTransferNode && humanTransferNode.data) {
          // 创建新的配置对象
          const newConfig = {
            activeTab: humanTransferNode.data.activeTab || "config",
            label: humanTransferNode.data.label || "转人工",
            name: humanTransferNode.data.name || "转人工",
            description: humanTransferNode.data.description || "",
            nodeTypeConfig: {
              memory: false,
              memoryTurns: 1,
              defaultReply: "",
              notifyPerson: "staff1",
              delayMinutes: 5,
              replyMode: 3,
              inputs: [],
              outputs: [],
              content: "",
              isEnable: true,
              name: null,
              notifyMethods: null,
              structuredOutput: false,
              triggerConditions: [
                {
                  matchType: 1,
                  operatorType: 0,
                  keyWord: "",
                },
              ],
            },
            type: "humanTransfer",
            id: humanTransferNode.data.id || "",
          };

          // 如果存在nodeTypeConfig，则更新相关属性
          if (humanTransferNode.data.nodeTypeConfig) {
            const ntc = humanTransferNode.data.nodeTypeConfig;

            // 更新基本属性
            if (ntc.memory !== undefined)
              newConfig.nodeTypeConfig.memory = ntc.memory;
            if (ntc.memoryTurns !== undefined)
              newConfig.nodeTypeConfig.memoryTurns = ntc.memoryTurns;
            if (ntc.defaultReply !== undefined)
              newConfig.nodeTypeConfig.defaultReply = ntc.defaultReply;
            if (ntc.notifyPerson !== undefined)
              newConfig.nodeTypeConfig.notifyPerson = ntc.notifyPerson;
            if (ntc.delayMinutes !== undefined)
              newConfig.nodeTypeConfig.delayMinutes = ntc.delayMinutes;
            if (ntc.replyMode !== undefined)
              newConfig.nodeTypeConfig.replyMode = ntc.replyMode;
            if (ntc.content !== undefined)
              newConfig.nodeTypeConfig.content = ntc.content;
            if (ntc.isEnable !== undefined)
              newConfig.nodeTypeConfig.isEnable = ntc.isEnable;
            if (ntc.name !== undefined)
              newConfig.nodeTypeConfig.name = ntc.name;
            if (ntc.structuredOutput !== undefined)
              newConfig.nodeTypeConfig.structuredOutput = ntc.structuredOutput;

            // 更新数组
            if (ntc.inputs) newConfig.nodeTypeConfig.inputs = ntc.inputs;
            if (ntc.outputs) newConfig.nodeTypeConfig.outputs = ntc.outputs;

            // 更新通知方法
            if (ntc.notifyMethods) {
              newConfig.nodeTypeConfig.notifyMethods = ntc.notifyMethods;
            }

            // 更新触发条件，包含operatorType字段
            if (ntc.triggerConditions) {
              newConfig.nodeTypeConfig.triggerConditions = ntc.triggerConditions.map(condition => ({
                matchType: condition.matchType || 1,
                operatorType: condition.operatorType || 0,
                keyWord: condition.keyWord || "",
              }));
            }
          }

          // 更新配置
          this.humanTransferConfig = newConfig;
        }
      }
    },
    handleConfig() {
      this.drawerVisible = true;
    },
    handleAddTrigger() {
      this.humanTransferConfig.nodeTypeConfig.triggerConditions.push({
        matchType: 1,
        operatorType: 0,
        keyWord: "",
      });
    },
    handleDeleteTrigger(index) {
      this.humanTransferConfig.nodeTypeConfig.triggerConditions.splice(
        index,
        1
      );
    },
    // 更新转人工配置，并通知父组件
    updateHumanTransferConfig() {
      this.$emit("update-human-transfer", this.humanTransferConfig);
    },

    // 更新响应配置，并通知父组件
    updateResponseConfig() {
      this.$emit("update-response-config", this.responseConfig);
    },

        handleSave() {
      // 触发自定义事件并传递配置数据
      this.updateHumanTransferConfig();
      this.updateResponseConfig();
      this.drawerVisible = false;
      this.$message.success("保存成功");
    },
  },
};
</script>

<style lang="scss" scoped>
.settings-content {
  padding: 20px;
  background: #fff;
}

.settings-card {
  background: #fff;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  .card-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;

    .main-title {
      font-size: 16px;
      font-weight: 500;
      color: #1f2937;
      margin: 0;
    }
  }
}

.settings-section {
  padding: 0 20px;
}

.config-row {
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .config-item {
    display: flex;
    align-items: center;
    padding: 16px 0;
    font-size: 14px;

    .item-label {
      flex: 1;
      color: #1f2937;
    }

    .item-status {
      width: 120px;

      .status-text {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #67c23a;

        i {
          font-size: 16px;
        }

        &.status-disabled {
          color: #f56c6c;
        }
      }
    }

        .item-action {
      width: 80px;
      text-align: right;
    }
  }
}

:deep(.config-drawer) {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 16px;
    font-weight: 500;
    color: #1f2937;
  }

  .el-drawer__body {
    padding: 0;
  }
}

.drawer-content {
  height: calc(100% - 72px);
  overflow-y: auto;
  padding: 20px;
}

.config-section {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 24px;
  border: 1px solid #f0f0f0;

  &:last-child {
    margin-bottom: 0;
  }

  .section-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: #1f2937;
    }
  }

  .section-content {
    padding: 20px;
    background: #f9fafb;

    .label {
      color: #606266;
      margin-right: 8px;
    }

    .el-icon-question {
      color: #909399;
      cursor: pointer;
      font-size: 14px;
    }
  }
}

.trigger-item {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  .trigger-header {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-size: 14px;
      color: #1f2937;
      font-weight: 500;
    }
  }

  .trigger-content {
    padding: 16px;
  }

  .trigger-row {
    display: flex;
    gap: 16px;
    align-items: flex-start;
  }

  .trigger-col {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;

    &:last-child {
      flex: 0 0 auto;
      align-self: flex-end;
    }

    .label {
      font-size: 14px;
      color: #606266;
    }

    :deep(.el-select) {
      width: 100%;
    }

    :deep(.el-input) {
      width: 100%;
    }

    .delete-btn {
      margin-top: 24px;
      color: #f56c6c;

      &:hover {
        color: #f78989;
      }
    }
  }
}

.add-trigger {
  text-align: center;
  margin-top: 16px;

  :deep(.el-button--dashed) {
    width: 100%;
    border-style: dashed;
    border-color: #d9d9d9;

    &:hover {
      border-color: var(--el-color-primary);
      color: var(--el-color-primary);
    }
  }
}

.reply-row,
.notify-row,
.summary-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  &.mt-16 {
    margin-top: 16px;
  }
}

.notify-options {
  display: flex;
  gap: 24px;
}

.summary-item {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  .summary-header {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-size: 14px;
      color: #1f2937;
      font-weight: 500;
    }
  }

  .summary-content {
    padding: 16px;
  }

  .summary-row {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
      text-align: right;
    }
  }

  .summary-col {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .label {
      font-size: 14px;
      color: #606266;
    }

    :deep(.el-input) {
      width: 100%;

      .el-input__append {
        cursor: pointer;
      }
    }

    .delete-btn {
      color: #f56c6c;

      &:hover {
        color: #f78989;
      }
    }
  }
}

.add-summary {
  text-align: center;
  margin-top: 16px;

  :deep(.el-button--dashed) {
    width: 100%;
    border-style: dashed;
    border-color: #d9d9d9;

    &:hover {
      border-color: var(--el-color-primary);
      color: var(--el-color-primary);
    }
  }
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  text-align: right;

  .el-button + .el-button {
    margin-left: 12px;
  }
}

/* 调试模式样式 */
.settings-content {
  &.debug-layout {
    .debug-container {
      display: flex;
      height: calc(100vh - 120px); // 减去顶部菜单和标题的高度
      gap: 10px; // 减少间隙，与模块间距保持一致
      width: 100%;
      overflow: hidden; // 防止横向滚动

      .config-area {
        width: calc(50% - 5px); // 减去一半gap的宽度
        flex-shrink: 0;
        overflow-y: auto;
        padding-right: 0; // 移除padding避免挤压

        .config-sections {
          display: flex;
          flex-direction: column;
          gap: 16px; // 保持与原始布局一致的间距

          .settings-card {
            background: #fff;
            border-radius: 8px;
            border: 1px solid #f0f0f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            padding: 20px;
            height: auto;
            min-height: 200px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }

      .debug-area {
        width: calc(50% - 5px); // 减去一半gap的宽度
        background: #fff;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        overflow: hidden;

        .debug-panel {
          height: 100%;
          display: flex;
          flex-direction: column;

          .debug-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 20px 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            flex-shrink: 0;

            h3 {
              margin: 0;
              font-size: 16px;
              font-weight: 500;
              color: #1f2937;
            }
          }

          .debug-content {
            flex: 1;
            overflow: hidden;
            padding: 20px;

            .debug-chat-container {
              height: 100%;
              overflow: hidden;

              :deep(.debug-chat-panel) {
                height: 100%;
                border: none;
                box-shadow: none;
              }
            }
          }
        }
      }
    }
  }
}

// 关闭调试按钮样式
.close-debug-btn {
  color: #909399;
  padding: 0;

  &:hover {
    color: #f56c6c;
  }

  i {
    font-size: 16px;
  }
}
</style>

