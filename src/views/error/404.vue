<template>
  <div class="error-404">
    <div class="error-content">
      <div class="error-icon">
        <i class="el-icon-warning-outline"></i>
      </div>
      <h1>404</h1>
      <h2>抱歉，您访问的页面不存在</h2>
      <div class="error-actions">
        <el-button type="primary" @click="goHome">返回首页</el-button>
        <el-button @click="goBack">返回上一页</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ErrorPage404',
  methods: {
    goHome() {
      this.$router.push('/')
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.error-404 {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;

  .error-content {
    text-align: center;
    padding: 40px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .error-icon {
      font-size: 120px;
      color: #409EFF;
      margin-bottom: 24px;
    }

    h1 {
      font-size: 72px;
      color: #409EFF;
      margin: 0 0 16px;
      font-weight: 600;
    }

    h2 {
      font-size: 24px;
      color: #606266;
      margin: 0 0 32px;
      font-weight: 400;
    }

    .error-actions {
      .el-button + .el-button {
        margin-left: 16px;
      }
    }
  }
}
</style>
